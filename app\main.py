#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI数据分析平台 V2.0 - 基于模块化架构

使用新的core模块架构，遵循Streamlit最佳实践。
"""

import streamlit as st

# 设置页面配置 - 必须在所有其他Streamlit命令之前调用
st.set_page_config(
    page_title="AI数据分析平台 V2.0",
    page_icon="🤖",
    layout="centered",  # 使用默认的居中布局，提供更好的阅读体验
    initial_sidebar_state="expanded"
)

import pandas as pd
import os
import json
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# 导入新的core模块
from core import LLMFactory, TongyiConfig
from core.integrations.streamlit_integration import StreamlitLLMIntegration
from core.utils.validators import validate_file_upload
from core.metadata.metadata_ui import MetadataUI

# 导入统一配置
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))
from config.app_settings import get_config

# 加载环境变量
load_dotenv()

# 获取配置
app_config = get_config()

# 页面配置已在文件开头设置

def format_chat_message(message, integration=None):
    """格式化聊天消息显示"""
    role = message["role"]
    content = message["content"]
    timestamp = message.get("timestamp", "")
    code = message.get("code", "")
    exec_result = message.get("exec_result", "")

    # 显示时间戳
    if timestamp:
        st.caption(f"🕒 {timestamp}")

    # 显示消息内容
    st.markdown(content)

    # 如果是助手消息且包含代码，显示代码和执行结果
    if role == "assistant" and code:
        # 显示代码（可折叠）
        with st.expander("📝 查看生成的代码", expanded=False):
            st.code(code, language='python')

        # 如果有执行结果且成功，重新执行代码显示结果
        if exec_result and exec_result.get("success") and integration:
            st.markdown("📊 **执行结果：**")
            try:
                # 重新执行代码以显示结果（用于历史消息）
                execute_code_in_chat_message(integration, code)
            except Exception as e:
                st.error(f"重新执行代码时出错: {str(e)}")
        elif exec_result and not exec_result.get("success"):
            # 显示执行错误
            st.error(f"❌ 代码执行失败: {exec_result.get('error', '未知错误')}")

def execute_code_in_chat_message(integration, code):
    """在聊天消息容器内执行代码并直接显示结果"""
    try:
        # 准备执行环境
        exec_globals = {
            'st': st,
            'pd': pd,
            'df': st.session_state.current_data,
            'data': st.session_state.current_data,
        }

        # 导入常用库
        import numpy as np
        import matplotlib.pyplot as plt
        exec_globals.update({
            'np': np,
            'plt': plt,
        })

        # 添加优化的显示函数 - 直接在当前容器中显示
        def show_dataframe_html(df, title="数据表格", max_rows=100):
            """在聊天消息中显示DataFrame为HTML表格"""
            if isinstance(df, pd.DataFrame):
                st.subheader(title)
                html_content = integration.format_dataframe_as_html(df, max_rows)
                st.markdown(html_content, unsafe_allow_html=True)
            else:
                st.write(df)

        def show_chart(fig=None, title="图表", use_container_width=True):
            """在聊天消息中显示图表"""
            if fig is not None:
                st.subheader(title)
                st.pyplot(fig, use_container_width=use_container_width)
            else:
                # 如果没有传入图表对象，显示当前的matplotlib图表
                st.subheader(title)
                st.pyplot(plt.gcf(), use_container_width=use_container_width)

        def show_plotly_chart(fig, title="交互式图表", use_container_width=True):
            """在聊天消息中显示Plotly交互式图表"""
            st.subheader(title)
            st.plotly_chart(fig, use_container_width=use_container_width)

        exec_globals.update({
            'show_dataframe_html': show_dataframe_html,
            'show_chart': show_chart,
            'show_plotly_chart': show_plotly_chart
        })

        # 执行代码
        exec(code, exec_globals)

        return True, ""

    except Exception as e:
        error_msg = f"代码执行失败: {str(e)}"
        st.error(error_msg)
        return False, error_msg

def save_chat_history():
    """保存聊天历史到文件"""
    if 'chat_messages' in st.session_state and st.session_state.chat_messages:
        # 创建聊天历史目录
        chat_dir = Path("chat_history")
        chat_dir.mkdir(exist_ok=True)

        # 生成文件名（基于当前时间）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = chat_dir / f"chat_{timestamp}.json"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(st.session_state.chat_messages, f, ensure_ascii=False, indent=2)
            return str(filename)
        except Exception as e:
            st.error(f"保存聊天历史失败: {e}")
            return None
    return None

def load_chat_history(filename):
    """从文件加载聊天历史"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            messages = json.load(f)
        st.session_state.chat_messages = messages
        return True
    except Exception as e:
        st.error(f"加载聊天历史失败: {e}")
        return False

def get_chat_history_files():
    """获取所有聊天历史文件"""
    chat_dir = Path("chat_history")
    if not chat_dir.exists():
        return []

    files = []
    for file in chat_dir.glob("chat_*.json"):
        try:
            # 从文件名提取时间戳
            timestamp_str = file.stem.replace("chat_", "")
            if len(timestamp_str) >= 15:  # YYYYMMDD_HHMMSS
                timestamp = datetime.strptime(timestamp_str[:15], "%Y%m%d_%H%M%S")
                files.append({
                    "path": str(file),
                    "name": file.name,
                    "timestamp": timestamp,
                    "display_name": timestamp.strftime("%Y-%m-%d %H:%M:%S")
                })
        except:
            continue

    # 按时间倒序排列
    files.sort(key=lambda x: x["timestamp"], reverse=True)
    return files

# 全局样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    .status-box {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    .chat-container {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 10px 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .sidebar-section {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .metric-card {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 15px;
        margin: 5px 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border-left: 4px solid #1f77b4;
    }
    .stChatMessage {
        background-color: #ffffff;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .stExpander {
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* 全屏布局样式 */
    .main-content-full {
        width: 100%;
        max-width: none;
        padding: 0;
    }

    /* 专注于聊天消息修复 - 移除悬浮框样式 */



    /* 隐藏Streamlit默认的全屏按钮区域 */
    .stApp > header {
        background-color: transparent;
    }

    /* 优化主容器 */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
        max-width: none;
    }

    /* 聊天容器全屏优化 */
    .chat-container {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 10px 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        width: 100%;
        max-width: none;
    }

    /* 修复聊天消息显示问题 - 使用更精确的选择器 */
    [data-testid="stChatMessage"] {
        background-color: #ffffff !important;
        border-radius: 10px !important;
        padding: 15px !important;
        margin: 10px 0 !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
        color: #333333 !important;
    }

    /* 修复用户消息样式 */
    [data-testid="stChatMessage"][data-testid*="user"] {
        background-color: #e3f2fd !important;
        border-left: 4px solid #2196f3 !important;
    }

    /* 修复助手消息样式 */
    [data-testid="stChatMessage"][data-testid*="assistant"] {
        background-color: #f1f8e9 !important;
        border-left: 4px solid #4caf50 !important;
    }

    /* 更广泛的聊天消息修复 */
    .stChatMessage,
    [class*="stChatMessage"],
    [class*="chat-message"] {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-radius: 10px !important;
        padding: 15px !important;
        margin: 10px 0 !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
    }

    /* 确保聊天消息文本可见 */
    [data-testid="stChatMessage"] .stMarkdown,
    [data-testid="stChatMessage"] p,
    [data-testid="stChatMessage"] div {
        color: #333333 !important;
    }

    /* 修复聊天输入框样式 */
    [data-testid="stChatInput"],
    .stChatInput {
        background-color: #ffffff !important;
        border-radius: 8px !important;
        border: 1px solid #ddd !important;
    }

    /* 修复初始聊天容器显示问题 */
    .chat-container {
        background-color: #f8f9fa !important;
        color: #333333 !important;
    }

    /* 确保所有聊天相关元素都有正确的颜色 */
    .chat-container * {
        color: #333333 !important;
    }

    /* 修复Streamlit默认的聊天消息容器 */
    [data-testid="stChatMessageContainer"] {
        background-color: #ffffff !important;
        color: #333333 !important;
    }

    /* 修复聊天消息内容 */
    [data-testid="stChatMessageContent"] {
        color: #333333 !important;
    }

    /* 修复初始欢迎消息显示 */
    .stChatMessage:first-child {
        background-color: #f1f8e9 !important;
        border-left: 4px solid #4caf50 !important;
        color: #333333 !important;
    }

    /* 强制修复所有可能的聊天元素 */
    [class*="chat"] {
        color: #333333 !important;
    }

    /* 修复markdown内容 */
    .stMarkdown {
        color: #333333 !important;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """主应用函数"""

    # 标题
    st.markdown('<h1 class="main-header">🤖 AI数据分析平台 V2.0</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # 初始化集成实例
    if 'integration' not in st.session_state:
        st.session_state.integration = StreamlitLLMIntegration()

    # 初始化界面状态
    if 'show_metadata_ui' not in st.session_state:
        st.session_state.show_metadata_ui = False

    integration = st.session_state.integration

    # 尝试自动初始化LLM
    integration.auto_initialize_llm()

    # 尝试自动加载最近文件
    integration.auto_load_recent_file()

    # 如果需要显示元数据管理界面
    if st.session_state.show_metadata_ui:
        MetadataUI.render_metadata_management()

        # 返回按钮
        if st.button("🔙 返回主界面"):
            st.session_state.show_metadata_ui = False
            st.rerun()
        return
    
    # 侧边栏配置
    with st.sidebar:
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.header("⚙️ 系统配置")

        # 显示系统状态概览
        if integration.is_llm_ready():
            st.success("🤖 LLM已就绪")
        else:
            st.warning("🤖 LLM未初始化")

        if st.session_state.current_data is not None:
            st.success(f"📊 数据已加载")
        else:
            st.info("📊 等待数据上传")

        st.markdown('</div>', unsafe_allow_html=True)

        # LLM配置
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.subheader("🤖 LLM设置")
        
        # API密钥输入
        api_key = st.text_input(
            "通义千问API密钥",
            type="password",
            value=app_config.llm.tongyi_api_key,
            help="请输入您的通义千问API密钥"
        )
        
        # 模型选择
        available_models = LLMFactory.get_available_models()
        model_options = list(available_models.keys())
        model = st.selectbox(
            "选择模型",
            options=model_options,
            index=0,
            help="选择要使用的通义千问模型"
        )
        
        # 高级设置
        with st.expander("🔧 高级设置"):
            temperature = st.slider("Temperature", 0.0, 1.0, app_config.llm.tongyi_temperature, 0.1)
            max_tokens = st.slider("Max Tokens", 500, 4000, app_config.llm.tongyi_max_tokens, 100)
            enable_chart_fix = st.checkbox("启用图表修复", value=app_config.llm.enable_chart_fix)
            enable_metadata = st.checkbox("启用元数据支持", value=app_config.llm.enable_metadata)
        
        # 初始化LLM
        if st.button("🚀 初始化LLM", type="primary"):
            if api_key:
                with st.spinner("正在初始化LLM..."):
                    success, error_msg = integration.setup_llm(
                        api_key=api_key,
                        model=model,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        enable_chart_fix=enable_chart_fix,
                        enable_metadata=enable_metadata
                    )
                
                if success:
                    st.success("✅ LLM初始化成功！")
                    st.rerun()
                else:
                    st.error(f"❌ LLM初始化失败: {error_msg}")
            else:
                st.error("❌ 请输入API密钥")

        st.markdown('</div>', unsafe_allow_html=True)
        st.markdown("---")

        # 聊天历史管理
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.subheader("💬 聊天历史")

        # 显示当前聊天统计
        if 'chat_messages' in st.session_state:
            total_messages = len(st.session_state.chat_messages)
            user_messages = len([m for m in st.session_state.chat_messages if m["role"] == "user"])
            st.info(f"📊 当前会话: {total_messages} 条消息 ({user_messages} 个问题)")

        # 保存当前聊天历史
        if st.button("💾 保存当前聊天", help="将当前聊天历史保存到文件"):
            filename = save_chat_history()
            if filename:
                st.success(f"✅ 聊天历史已保存: {Path(filename).name}")

        # 加载历史聊天
        chat_files = get_chat_history_files()
        if chat_files:
            st.write("📂 **历史聊天记录:**")
            selected_file = st.selectbox(
                "选择要加载的聊天记录",
                options=[None] + [f["path"] for f in chat_files],
                format_func=lambda x: "选择聊天记录..." if x is None else next(f["display_name"] for f in chat_files if f["path"] == x),
                key="chat_history_selector"
            )

            if selected_file and st.button("📥 加载选中的聊天", help="加载选中的聊天历史"):
                if load_chat_history(selected_file):
                    st.success("✅ 聊天历史加载成功！")
                    st.rerun()

        # 搜索聊天历史
        if 'chat_messages' in st.session_state and len(st.session_state.chat_messages) > 1:
            st.write("🔍 **搜索当前聊天:**")
            search_term = st.text_input("搜索关键词", key="chat_search")
            if search_term:
                matching_messages = []
                for i, message in enumerate(st.session_state.chat_messages):
                    if search_term.lower() in message["content"].lower():
                        matching_messages.append((i, message))

                if matching_messages:
                    st.write(f"找到 {len(matching_messages)} 条匹配消息:")
                    for i, (idx, message) in enumerate(matching_messages[:3]):  # 只显示前3条
                        with st.expander(f"消息 #{idx} - {message['role']}", expanded=False):
                            st.write(f"**时间:** {message.get('timestamp', '未知')}")
                            st.write(f"**内容:** {message['content'][:100]}...")
                else:
                    st.info("未找到匹配的消息")

        st.markdown('</div>', unsafe_allow_html=True)
        st.markdown("---")

        # 元数据管理入口
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        if st.button("🎯 元数据管理", help="管理数据的业务含义和列描述"):
            st.session_state.show_metadata_ui = True
            st.rerun()
        st.markdown('</div>', unsafe_allow_html=True)

        st.markdown("---")

        # 文件上传
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.subheader("📁 数据上传")
        uploaded_file = st.file_uploader(
            "选择数据文件",
            type=[ext.lstrip('.') for ext in app_config.data.supported_file_types],
            help=f"支持格式: {', '.join(app_config.data.supported_file_types)}"
        )
        
        if uploaded_file is not None:
            # 保存上传的文件
            file_path = app_config.data.uploaded_files_dir / uploaded_file.name
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            # 验证文件
            is_valid, error_msg = validate_file_upload(str(file_path))
            if is_valid:
                # 加载数据
                try:
                    if uploaded_file.name.endswith('.csv'):
                        data = pd.read_csv(file_path)
                    elif uploaded_file.name.endswith(('.xlsx', '.xls')):
                        data = pd.read_excel(file_path)
                    elif uploaded_file.name.endswith('.json'):
                        data = pd.read_json(file_path)
                    else:
                        st.error("不支持的文件格式")
                        return
                    
                    # 加载到集成实例
                    success, error_msg = integration.load_data(data, uploaded_file.name)
                    if success:
                        st.success(f"✅ 数据加载成功: {uploaded_file.name}")

                        # 显示元数据快速设置
                        with st.expander("🎯 元数据设置", expanded=False):
                            MetadataUI.render_quick_metadata_setup(uploaded_file.name, data)
                    else:
                        st.error(f"❌ 数据加载失败: {error_msg}")
                
                except Exception as e:
                    st.error(f"❌ 文件读取失败: {str(e)}")
            else:
                st.error(f"❌ 文件验证失败: {error_msg}")

        st.markdown('</div>', unsafe_allow_html=True)
    
    # 系统状态显示 - 使用Streamlit原生组件替代自定义悬浮框
    # 在侧边栏显示系统状态，这样更符合Streamlit的设计模式
    with st.sidebar:
        st.markdown("### 📊 系统状态")

        # LLM状态
        llm_ready = integration.is_llm_ready()
        if llm_ready:
            st.success("🤖 LLM: 已就绪")
        else:
            st.error("🤖 LLM: 未初始化")

        # 数据状态
        data_loaded = st.session_state.current_data is not None
        if data_loaded:
            st.success("📊 数据: 已加载")
            st.info(f"📁 文件: {st.session_state.get('data_name', '无')}")
            st.info(f"📏 形状: {st.session_state.current_data.shape}")
        else:
            st.error("📊 数据: 未加载")

        st.markdown("---")

        # 全屏按钮
        if st.button("🔍 全屏模式", help="进入全屏模式进行数据分析", use_container_width=True):
            st.markdown("""
            <script>
            // 请求全屏
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen();
            } else if (document.documentElement.webkitRequestFullscreen) {
                document.documentElement.webkitRequestFullscreen();
            } else if (document.documentElement.msRequestFullscreen) {
                document.documentElement.msRequestFullscreen();
            }
            </script>
            """, unsafe_allow_html=True)

    # 简化的页面优化
    page_optimization_js = """
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 延迟执行，确保页面完全加载
        setTimeout(function() {
            // 隐藏Streamlit的默认工具栏
            const toolbar = document.querySelector('[data-testid="stToolbar"]');
            if (toolbar) {
                toolbar.style.display = 'none';
            }

            // 隐藏Streamlit的header
            const header = document.querySelector('header[data-testid="stHeader"]');
            if (header) {
                header.style.display = 'none';
            }

            // 优化主容器
            const mainContainer = document.querySelector('.main');
            if (mainContainer) {
                mainContainer.style.paddingTop = '0';
            }
        }, 1000);
    });
    </script>
    """

    st.markdown(page_optimization_js, unsafe_allow_html=True)

    # 主界面 - 全屏布局
    st.markdown('<div class="main-content-full">', unsafe_allow_html=True)

    st.markdown('<div class="chat-container">', unsafe_allow_html=True)
    st.subheader("💬 AI数据分析")

    # 检查系统是否就绪
    if not integration.is_llm_ready():
        st.warning("⚠️ 请先在侧边栏配置并初始化LLM")
        return

    if st.session_state.current_data is None:
        st.warning("⚠️ 请先在侧边栏上传数据文件")
        return

    # 初始化聊天历史 - 按照Streamlit最佳实践
    if 'chat_messages' not in st.session_state:
        welcome_message = {
            "role": "assistant",
            "content": "您好！我是您的AI数据分析助手。请告诉我您想要分析什么？",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        st.session_state.chat_messages = [welcome_message]

    # 显示聊天历史
    for message in st.session_state.chat_messages:
        with st.chat_message(message["role"]):
            format_chat_message(message, integration)

    # 数据预览
    with st.expander("👀 数据预览", expanded=False):
        data = st.session_state.current_data
        st.write(f"**数据形状**: {data.shape}")
        st.write(f"**列名**: {list(data.columns)}")
        st.dataframe(data.head(), use_container_width=True)

    # 聊天输入
    if prompt := st.chat_input("请输入您的分析需求..."):
        # 添加用户消息（带时间戳）
        user_message = {
            "role": "user",
            "content": prompt,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        st.session_state.chat_messages.append(user_message)

        with st.chat_message("user"):
            format_chat_message(user_message, integration)

        # AI分析和回复
        with st.chat_message("assistant"):
            # 显示时间戳
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            st.caption(f"🕒 {timestamp}")

            with st.spinner("🤖 正在分析数据..."):
                # 分析选项（默认值）
                use_metadata = st.session_state.get('default_use_metadata', False)
                success, code, error_msg = integration.analyze_data(prompt, use_metadata)

            if success:
                st.markdown("✅ **分析完成！我已经根据您的要求生成并执行了相应的代码。**")

                # 显示代码（可折叠）
                with st.expander("📝 查看生成的代码", expanded=False):
                    st.code(code, language='python')

                # 直接在聊天消息中执行代码并显示结果
                st.markdown("📊 **执行结果：**")
                exec_success, exec_error = execute_code_in_chat_message(integration, code)

                # 准备助手消息
                if exec_success:
                    response_content = f"✅ 分析完成！我已经根据您的要求「{prompt}」生成并执行了相应的代码。结果已显示在上方。"
                else:
                    response_content = f"❌ 代码执行失败: {exec_error}"

                assistant_message = {
                    "role": "assistant",
                    "content": response_content,
                    "timestamp": timestamp,
                    "code": code,
                    "exec_result": {
                        "success": exec_success,
                        "error": exec_error if not exec_success else None
                    }
                }
            else:
                response_content = f"❌ 分析失败: {error_msg}"
                st.error(response_content)

                assistant_message = {
                    "role": "assistant",
                    "content": response_content,
                    "timestamp": timestamp,
                    "code": "",
                    "exec_result": {"success": False, "error": error_msg}
                }

            # 添加到聊天历史
            st.session_state.chat_messages.append(assistant_message)

    # 分析选项设置
    with st.expander("⚙️ 分析选项", expanded=False):
        st.session_state.default_use_metadata = st.checkbox(
            "默认使用元数据增强",
            value=st.session_state.get('default_use_metadata', False),
            help="使用数据元数据提供更精确的分析"
        )

    # 聊天管理选项
    with st.expander("💬 聊天管理", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            if st.button("🗑️ 清空聊天记录", use_container_width=True):
                welcome_message = {
                    "role": "assistant",
                    "content": "您好！我是您的AI数据分析助手。请告诉我您想要分析什么？",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                st.session_state.chat_messages = [welcome_message]
                st.rerun()

        with col2:
            if st.button("📤 导出聊天记录", use_container_width=True):
                if 'chat_messages' in st.session_state and st.session_state.chat_messages:
                    # 创建导出数据
                    export_data = {
                        "export_time": datetime.now().isoformat(),
                        "total_messages": len(st.session_state.chat_messages),
                        "messages": st.session_state.chat_messages
                    }

                    # 转换为JSON字符串
                    json_str = json.dumps(export_data, ensure_ascii=False, indent=2)

                    # 提供下载
                    st.download_button(
                        label="⬇️ 下载JSON文件",
                        data=json_str,
                        file_name=f"chat_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json",
                        use_container_width=True
                    )

        # 显示聊天统计
        if 'chat_messages' in st.session_state:
            messages = st.session_state.chat_messages
            user_msgs = [m for m in messages if m["role"] == "user"]
            assistant_msgs = [m for m in messages if m["role"] == "assistant"]

            st.write("📊 **聊天统计:**")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("总消息", len(messages))
            with col2:
                st.metric("用户问题", len(user_msgs))
            with col3:
                st.metric("AI回复", len(assistant_msgs))

            # 显示最近的问题
            if user_msgs:
                st.write("🔍 **最近的问题:**")
                for i, msg in enumerate(reversed(user_msgs[-3:])):  # 显示最近3个问题
                    with st.expander(f"问题 {len(user_msgs)-i}: {msg['content'][:50]}...", expanded=False):
                        st.write(f"**时间:** {msg.get('timestamp', '未知')}")
                        st.write(f"**完整问题:** {msg['content']}")

                        # 找到对应的回答
                        msg_idx = messages.index(msg)
                        if msg_idx + 1 < len(messages) and messages[msg_idx + 1]["role"] == "assistant":
                            answer = messages[msg_idx + 1]
                            st.write(f"**AI回答:** {answer['content'][:200]}...")
                            if answer.get('code'):
                                st.code(answer['code'][:300] + "..." if len(answer['code']) > 300 else answer['code'], language='python')

    st.markdown('</div>', unsafe_allow_html=True)

    st.markdown('</div>', unsafe_allow_html=True)  # 关闭main-content-full

    # 页脚
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center; color: #666; font-size: 0.9rem;'>
            🚀 AI数据分析平台 V2.0 | 基于模块化架构 | 
            <a href='https://github.com' target='_blank'>GitHub</a>
        </div>
        """,
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
